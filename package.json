{"name": "ecocoil-website", "private": true, "type": "module", "scripts": {"build": "react-router build", "cf-typegen": "wrangler types", "deploy": "npm run build && wrangler deploy", "dev": "react-router dev", "postinstall": "npm run cf-typegen", "preview": "npm run build && vite preview", "typecheck": "npm run cf-typegen && react-router typegen && tsc -b"}, "dependencies": {"isbot": "^5.1.27", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.1"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.0.12", "@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^4.35.0"}}